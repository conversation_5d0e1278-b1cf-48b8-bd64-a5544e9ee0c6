package spinal.tester.pending

import spinal.core._
import spinal.core.sim._
import spinal.lib.com.i2c._
import spinal.lib.com.i2c.sim._
import spinal.lib._

// Simple adder for testing Windows platform process termination issue
case class MyAdder() extends Component {
  val io = new Bundle {
    val a = in UInt(8 bits)
    val b = in UInt(8 bits)
    val result = out UInt(8 bits)
  }
  io.result := io.a + io.b
}

object MyAdderSim extends App {
  
  println("Starting Windows platform process termination test...")
  println("Testing different Verilator thread configurations...")
  
  // Test with different Verilator thread configurations
  def testWithVerilatorFlags(testName: String, flags: List[String]): Unit = {
    println(s"\n=== Testing $testName with flags: ${flags.mkString(" ")} ===")
    
    try {
      val config = flags.foldLeft(SimConfig.withWave) { (config, flag) =>
        config.addSimulatorFlag(flag)
      }
      
      config.doSim(MyAdder()) { dut =>
        dut.clockDomain.forkStimulus(period = 10)
        
        for (i <- 0 until 3) {
          dut.io.a #= i
          dut.io.b #= i + 1
          dut.clockDomain.waitSampling()
          println(s"$testName - Cycle $i: ${i} + ${i + 1} = ${dut.io.result.toInt}")
        }
        
        println(s"$testName completed successfully!")
      }
      
      println(s"$testName finished - checking if process exits...")
      Thread.sleep(2000) // Wait 2 seconds to see if process hangs
      println(s"$testName - Process continued normally after 2 seconds")
      
    } catch {
      case e: Exception =>
        println(s"$testName failed with exception: ${e.getMessage}")
        e.printStackTrace()
    }
  }
  
  // Test different configurations to identify the hanging cause
  try {
    // Test 1: Default configuration (should hang on v5.004+)
    testWithVerilatorFlags("Default", List(""))
    
    // Test 2: Explicitly set single thread (new v5.004 way)
    //testWithVerilatorFlags("SingleThread-New", List("--threads", "1"))
    
    // Test 3: Try to force old threading behavior (if available)
    // testWithVerilatorFlags("OldThreading", List("--no-threads"))
    
    // Test 4: Disable timing features that might cause issues
    //testWithVerilatorFlags("NoTiming", List("--no-timing"))
    
    // Test 5: Try with different optimization levels
    //testWithVerilatorFlags("LowOptimization", List("-O0"))
    
    // Test 6: Combination of thread and timing flags
    //testWithVerilatorFlags("ThreadsAndTiming", List("--no-trace"))
    
    println("\n=== All tests completed ===")
    
  } catch {
    case e: Exception =>
      println(s"Test suite failed: ${e.getMessage}")
      e.printStackTrace()
  }
}

object testObject1 extends App {
  def spinal = SpinalConfig(
    defaultConfigForClockDomains = ClockDomainConfig(
      resetActiveLevel = HIGH
    ),
    onlyStdLogicVectorAtTopLevelIo = false,
  )

  val compile = SimConfig.withConfig(spinal).withFstWave.addSimulatorFlag("--threads 4").compile(new Component {
    val io = new Bundle {
      val i = in Bool()
      val o = out Bool()
    }

    io.o := io.i
  })

  compile.doSim("1")(dut => {
    dut.io.i #= false
    dut.clockDomain.forkStimulus(10)
    dut.clockDomain.waitRisingEdge(2)
    assert(!dut.io.o.toBoolean)
    println("doSim1 Simulation successful!")
  })

  compile.doSim("2")(dut => {
    dut.io.i #= true
    dut.clockDomain.forkStimulus(10)
    dut.clockDomain.waitRisingEdge(2)
    assert(dut.io.o.toBoolean)
    println("doSim2 Simulation successful!")
  })

  compile.doSim("3")(dut => {
  dut.io.i #= false
  dut.clockDomain.forkStimulus(10)
  dut.clockDomain.waitRisingEdge(2)
  println("doSim3 Simulation successful!")
})

// 最简I2C测试案例
object SimpleI2cTest extends App {
  println("开始I2C最简测试案例...")

  // 创建一个简单的I2C从设备测试
  case class SimpleI2cTestBench() extends Component {
    val io = new Bundle {
      val i2c = master(I2c())
      val interrupt = out Bool()
    }

    // 实例化I2C从设备控制器
    val i2cSlave = new I2cSlave(I2cSlaveGenerics(
      samplingWindowSize = 3,
      samplingClockDividerWidth = 10 bits,
      timeoutWidth = 20 bits
    ))

    // 连接I2C接口
    io.i2c <> i2cSlave.io.i2c

    // 配置I2C从设备
    i2cSlave.io.config.samplingClockDivider := 100
    i2cSlave.io.config.timeout := 1000
    i2cSlave.io.config.tsuData := 5
    i2cSlave.io.config.timeoutClear := False

    // 简单的总线响应逻辑
    i2cSlave.io.bus.rsp.valid := True
    i2cSlave.io.bus.rsp.enable := True
    i2cSlave.io.bus.rsp.data := False

    io.interrupt := i2cSlave.io.timeout
  }

  // 仿真配置 - 简化版本，专注于基本功能测试
  try {
    println("正在编译I2C测试电路...")
    val compiled = SimConfig.withWave.compile(SimpleI2cTestBench())

    println("开始仿真测试...")
    compiled.doSim { dut =>
      println("初始化仿真环境...")

      // 启动时钟
      dut.clockDomain.forkStimulus(period = 10)

      // 等待初始化
      dut.clockDomain.waitRisingEdge(10)

      // 测试1: 检查初始状态
      println("测试1: 检查I2C总线初始状态")
      println(s"SCL初始状态: ${dut.io.i2c.scl.read.toBoolean}")
      println(s"SDA初始状态: ${dut.io.i2c.sda.read.toBoolean}")
      println(s"中断状态: ${dut.io.interrupt.toBoolean}")

      // 测试2: 简单的时钟周期测试
      println("测试2: 运行几个时钟周期")
      for (i <- 0 until 10) {
        dut.clockDomain.waitRisingEdge()
        if (i % 3 == 0) {
          println(s"周期 $i: SCL=${dut.io.i2c.scl.read.toBoolean}, SDA=${dut.io.i2c.sda.read.toBoolean}")
        }
      }

      // 测试3: 检查I2C从设备的内部状态
      println("测试3: 检查I2C从设备内部状态")
      println(s"inFrame: ${dut.i2cSlave.io.internals.inFrame.toBoolean}")
      println(s"sdaRead: ${dut.i2cSlave.io.internals.sdaRead.toBoolean}")
      println(s"sclRead: ${dut.i2cSlave.io.internals.sclRead.toBoolean}")

      // 等待仿真稳定
      dut.clockDomain.waitRisingEdge(20)

      println("✓ I2C基础功能测试完成!")
      println("✓ 所有测试项目都通过了!")
    }

    println("🎉 I2C最简测试案例成功完成!")

  } catch {
    case e: Exception =>
      println(s"测试过程中出现错误: ${e.getMessage}")
      e.printStackTrace()
  }
}
}