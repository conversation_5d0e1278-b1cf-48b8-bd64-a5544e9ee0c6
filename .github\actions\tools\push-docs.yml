name: "Push scaladoc on gh-pages"

on:
  push:
    branches:
      - dev
  release:
    types:
      - published

jobs:
  push-docs:
    runs-on: ubuntu-22.04
    timeout-minutes: 30

    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Setup JDK
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 17
        cache: sbt
    - name: Build scaladoc
      run: sbt clean '++ 2.12.13' unidoc
    - name: Deploy 🚀
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        folder: target/scala-2.12/unidoc
        target-folder: ${{ github.ref_name }}
        single-commit: true
