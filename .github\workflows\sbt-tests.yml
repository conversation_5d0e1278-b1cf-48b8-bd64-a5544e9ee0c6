# 可复用工作流：SBT测试套件
# 这个工作流被其他工作流调用，执行完整的SpinalHDL编译和测试流程
name: Run sbt tests
on:
  workflow_call:
    inputs:
      scala_version:
        required: true
        type: string
      runner_os:
        required: true
        type: string
      github_sha:
        required: true
        type: string
      verilator_version:
        required: true
        type: string
      verilator_flags:
        required: false
        type: string
        default: ""
        description: "Additional Verilator compilation flags"

# 定义并行执行的测试任务组
jobs:
  # ========== 编译阶段 ==========
  # 这是整个测试流程的基础，所有其他测试都依赖于此阶段的编译产物
  compile:
    runs-on: ubuntu-latest                                    # 运行环境：Ubuntu最新版
    timeout-minutes: 90                                      # 超时限制：90分钟
    container:
      # 使用纯净的基础镜像（仅包含Verilator和构建工具，不包含预编译的SpinalHDL）
      # 这确保测试的是当前分支的代码，而不是镜像中预存的master分支代码
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    # 步骤1：签出当前分支/PR的源代码
    - uses: actions/checkout@v3
    # 步骤2：获取Git子模块（如果项目依赖外部子模块）
    - name: Get submodules
      shell: bash
      run: git submodule update --init --recursive
    # 步骤3：编译主要源码
    # ++${{ inputs.scala_version }} 指定使用传入的Scala版本进行编译
    - run: sbt ++${{ inputs.scala_version }} compile
    # 步骤4：编译测试代码
    - run: sbt ++${{ inputs.scala_version }} Test/compile
    # 步骤5：缓存编译产物
    # 将编译结果保存到GitHub Actions缓存中，供后续测试任务复用
    # 缓存键包含运行环境、提交SHA和Verilator版本，确保缓存的精确匹配
    - uses: actions/cache/save@v3
      with:
        path: |
          **/
        key: ${{ inputs.runner_os }}-compiled-${{ inputs.github_sha }}-${{ inputs.verilator_version }}
    
    # 🔍 编译完成后的磁盘使用情况报告 (用于Verilator版本对比分析)
    - name: Report disk usage after compile
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'compile-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== IDSL插件测试 ==========
  # 测试SpinalHDL的Scala编译器插件功能
  idslplugin-test:
    needs: compile                                           # 依赖编译阶段完成
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    - uses: actions/checkout@v3
    # 恢复编译阶段缓存的编译产物，避免重复编译
    - uses: ./.github/actions/get-compiled
    # 运行IDSL插件的单元测试
    - run: sbt ++${{ inputs.scala_version }} idslplugin/test
    
    # 🔍 IDSL插件测试完成后的磁盘使用情况报告
    - name: Report disk usage after idslplugin-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'idslplugin-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== IDSL载荷测试 ==========
  # 测试IDSL相关的载荷处理功能
  idslpayload-test:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 运行IDSL载荷相关的测试
    - run: sbt ++${{ inputs.scala_version }} idslpayload/test
    
    # 🔍 IDSL载荷测试完成后的磁盘使用情况报告
    - name: Report disk usage after idslpayload-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'idslpayload-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 核心功能测试 ==========
  # 测试SpinalHDL的核心语言功能（排除形式化验证和PSL测试）
  core-test:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 运行核心测试，但排除形式化验证标签(-l spinal.tester.formal)和PSL标签(-l spinal.tester.psl)
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.core.* -- -l spinal.tester.formal -l spinal.tester.psl'
    
    # 🔍 核心功能测试完成后的磁盘使用情况报告
    - name: Report disk usage after core-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'core-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 核心形式化验证测试 ==========
  # 专门测试SpinalHDL核心功能的形式化验证特性
  core-formal:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 仅运行带有形式化验证标签(-n spinal.tester.formal)的测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.core.* -- -n spinal.tester.formal'
    
    # 🔍 核心形式化验证测试完成后的磁盘使用情况报告
    - name: Report disk usage after core-formal
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'core-formal-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 核心PSL测试 ==========
  # 测试Property Specification Language (PSL) 相关功能
  core-psl:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 仅运行带有PSL标签(-n spinal.tester.psl)的测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.core.* -- -n spinal.tester.psl'
    
    # 🔍 核心PSL测试完成后的磁盘使用情况报告
    - name: Report disk usage after core-psl
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'core-psl-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 仿真后端测试 ==========
  # 测试SpinalHDL的仿真功能，这是当前修复Verilator兼容性的关键测试
  sim-test:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行仿真模块的测试，这将验证VerilatorBackend等仿真相关代码
    # 这是验证Verilator 5+兼容性修复是否成功的关键测试
    - run: sbt ++${{ inputs.scala_version }} sim/test
    
    # 🔍 仿真后端测试完成后的磁盘使用情况报告 (重点关注：Verilator版本对比)
    - name: Report disk usage after sim-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'sim-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 测试器功能测试 ==========
  # 测试SpinalHDL测试框架本身的功能（排除形式化验证和PSL）
  tester-test:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
      VM_DEFAULT_RULES: "0"  # 禁用预编译头文件
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行测试器相关的测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.tester.* -- -l spinal.tester.formal -l spinal.tester.psl'
    
    # 🔍 测试器功能测试完成后的磁盘使用情况报告 (重点关注：Verilator版本对比)
    - name: Report disk usage after tester-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'tester-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 测试器形式化验证测试 ==========
  tester-formal:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行测试器的形式化验证测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.tester.* -- -n spinal.tester.formal'
    
    # 🔍 测试器形式化验证测试完成后的磁盘使用情况报告
    - name: Report disk usage after tester-formal
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'tester-formal-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 测试器PSL测试 ==========
  tester-psl:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行测试器的PSL测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.tester.* -- -n spinal.tester.psl'
    
    # 🔍 测试器PSL测试完成后的磁盘使用情况报告
    - name: Report disk usage after tester-psl
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'tester-psl-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 库功能测试 ==========
  # 测试SpinalHDL标准库（lib模块）的功能
  lib-test:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
      VM_DEFAULT_RULES: "0"  # 禁用预编译头文件
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行库模块的测试，包括各种总线协议、IP核等
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.lib.* -- -l spinal.tester.formal -l spinal.tester.psl'
    
    # 🔍 库功能测试完成后的磁盘使用情况报告 (重点关注：Verilator版本对比)
    - name: Report disk usage after lib-test
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'lib-test-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 库形式化验证测试 ==========
  lib-formal:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行库模块的形式化验证测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.lib.* -- -n spinal.tester.formal'
    
    # 🔍 库形式化验证测试完成后的磁盘使用情况报告
    - name: Report disk usage after lib-formal
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'lib-formal-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

  # ========== 库PSL测试 ==========
  lib-psl:
    needs: compile
    runs-on: ubuntu-latest
    timeout-minutes: 90
    container:
      image: ghcr.io/jaynerlin/docker:${{ inputs.verilator_version }}
    env:
      SPINAL_VERILATOR_FLAGS: ${{ inputs.verilator_flags }}
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    # 设置Verilator编译选项
    - name: Configure Verilator options
      shell: bash
      run: |
        echo "🔧 设置Verilator编译选项: ${{ inputs.verilator_flags }}"
        echo "ℹ️  这些选项将应用于所有Verilator仿真编译过程"
    # 运行库模块的PSL测试
    - run: sbt ++${{ inputs.scala_version }} 'tester/testOnly spinal.lib.* -- -n spinal.tester.psl'
    
    # 🔍 库PSL测试完成后的磁盘使用情况报告
    - name: Report disk usage after lib-psl
      uses: ./.github/actions/print-disk-info
      with:
        job_name: 'lib-psl-verilator-${{ inputs.verilator_version }}'
        show_details: 'true'
      if: always()

# 📊 Verilator编译选项优化测试说明：
# 
# 🎯 测试目的：
#   通过在所有仿真测试任务中应用统一的Verilator编译选项，验证特定编译配置
#   对SpinalHDL项目的兼容性和性能影响，确保在优化的编译设置下系统的稳定性。
#
# 🔧 当前应用的Verilator编译选项：
#   - --fno-dfg：       禁用DFG(数据流图)优化器，避免潜在的优化相关问题
#   - -O1：             使用优化级别1，在编译时间和运行性能间取得平衡
#   - --output-groups 0： 禁用文件分组，生成单一的输出文件以简化构建流程
#   - --no-assert：     禁用断言检查，可能提升仿真性能并减少编译复杂度
#
# 🔍 受影响的测试任务：
#   - sim-test：        Verilator仿真核心功能，验证基础仿真兼容性
#   - tester-test：     SpinalHDL测试框架，确保测试工具在优化配置下正常工作
#   - tester-formal：   形式化验证测试，验证编译选项对验证流程的影响
#   - tester-psl：      PSL(Property Specification Language)测试
#   - lib-test：        标准库测试，验证复杂IP核在优化配置下的行为
#   - lib-formal：      标准库形式化验证测试
#   - lib-psl：         标准库PSL测试
#
# 📈 关键监控指标：
#   - 编译时间变化（优化级别对编译速度的影响）
#   - 磁盘使用情况（文件分组禁用对编译产物大小的影响）
#   - 测试通过率（验证编译选项的兼容性）
#   - 内存消耗情况（优化对资源使用的影响）
#
# 💡 分析建议：
#   1. 对比启用这些选项前后的测试执行时间和资源消耗
#   2. 重点关注任何测试失败情况，可能表明编译选项不兼容
#   3. 分析磁盘使用报告，了解文件分组禁用对存储需求的影响
#   4. 根据测试结果调整编译选项配置，优化CI构建效率
#   5. 如果测试稳定，可以考虑将这些选项作为默认配置
