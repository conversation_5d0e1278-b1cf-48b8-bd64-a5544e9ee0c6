package spinal.tester.pending

import spinal.core._
import spinal.core.sim._
import spinal.lib.com.i2c._
import spinal.lib._

/**
 * 快速I2C测试 - 最小化版本
 */
object QuickI2cTest extends App {
  
  // 最简单的I2C测试组件
  case class MinimalI2c() extends Component {
    val io = new Bundle {
      val scl_write = out Bool()
      val sda_write = out Bool()
      val scl_read = in Bool()
      val sda_read = in Bool()
    }
    
    // 创建I2C从设备
    val i2c = new I2cSlave(I2cSlaveGenerics(
      samplingWindowSize = 3,
      samplingClockDividerWidth = 8 bits,
      timeoutWidth = 16 bits
    ))
    
    // 简单配置
    i2c.io.config.samplingClockDivider := 10
    i2c.io.config.timeout := 100
    i2c.io.config.tsuData := 2
    i2c.io.config.timeoutClear := False
    
    // 简单响应
    i2c.io.bus.rsp.valid := True
    i2c.io.bus.rsp.enable := True
    i2c.io.bus.rsp.data := False
    
    // 连接输出
    io.scl_write := i2c.io.i2c.scl.write
    io.sda_write := i2c.io.i2c.sda.write
    i2c.io.i2c.scl.read := io.scl_read
    i2c.io.i2c.sda.read := io.sda_read
  }
  
  println("开始快速I2C测试...")
  
  try {
    SimConfig.withWave.doSim(MinimalI2c()) { dut =>
      // 启动时钟
      dut.clockDomain.forkStimulus(10)
      
      // 初始化输入
      dut.io.scl_read #= true
      dut.io.sda_read #= true
      
      // 等待几个周期
      dut.clockDomain.waitRisingEdge(10)
      
      // 检查输出
      println(s"SCL写: ${dut.io.scl_write.toBoolean}")
      println(s"SDA写: ${dut.io.sda_write.toBoolean}")
      
      // 模拟START条件
      dut.io.sda_read #= false
      dut.clockDomain.waitRisingEdge(5)
      dut.io.scl_read #= false
      dut.clockDomain.waitRisingEdge(5)
      
      // 模拟STOP条件
      dut.io.scl_read #= true
      dut.clockDomain.waitRisingEdge(5)
      dut.io.sda_read #= true
      dut.clockDomain.waitRisingEdge(5)
      
      println("✓ 快速I2C测试完成!")
    }
    
    println("🎉 测试成功!")
    
  } catch {
    case e: Exception =>
      println(s"测试失败: ${e.getMessage}")
      e.printStackTrace()
  }
}
