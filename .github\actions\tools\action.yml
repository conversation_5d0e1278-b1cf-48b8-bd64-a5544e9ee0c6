name: 'Install tools'
description: 'Install and setup tools to test SpinalHDL designs'

runs:
  using: "composite"
  steps:
    - name: Get submodules
      shell: bash
      run: git submodule update --init --recursive

    - id: cache-tools
      uses: actions/cache@v3
      with:
        path: |
          ~/tools
          ~/fpga_toolchain
          ~/verilator
          ~/ghdl-build
          ~/.cache/pip
        key: ${{ runner.os }}-tools-${{ hashFiles('tools.sh') }}

    - name: Setup env
      shell: bash
      run: |
        echo "$HOME/fpga-toolchain/bin" >> $GITHUB_PATH
        echo "$HOME/tools/bin" >> $GITHUB_PATH

    - name: Install generic tools
      shell: bash
      run: source tools.sh && install_packages && (cd ~/ && install_tools) && purge_cocotb

    - name: Install fpga_toolchain
      shell: bash
      run: source tools.sh && (cd ~/ && install_fpga_toolchain)
