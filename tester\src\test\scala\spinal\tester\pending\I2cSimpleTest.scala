package spinal.tester.pending

import spinal.core._
import spinal.core.sim._
import spinal.lib.com.i2c._
import spinal.lib._

/**
 * 最简的I2C测试案例
 * 
 * 这个测试案例演示了如何：
 * 1. 创建一个基本的I2C从设备
 * 2. 配置I2C参数
 * 3. 运行基础的仿真测试
 * 4. 检查I2C信号状态
 */
object I2cSimpleTest extends App {
  println("=== SpinalHDL I2C 最简测试案例 ===")
  
  // 定义测试用的I2C组件
  case class SimpleI2cComponent() extends Component {
    val io = new Bundle {
      val i2c = master(I2c())
      val interrupt = out Bool()
    }
    
    // 实例化I2C从设备控制器
    val i2cSlave = new I2cSlave(I2cSlaveGenerics(
      samplingWindowSize = 3,           // 采样窗口大小
      samplingClockDividerWidth = 10 bits, // 时钟分频器位宽
      timeoutWidth = 20 bits            // 超时计数器位宽
    ))
    
    // 连接I2C接口
    io.i2c <> i2cSlave.io.i2c
    
    // 配置I2C从设备参数
    i2cSlave.io.config.samplingClockDivider := 100  // 采样时钟分频
    i2cSlave.io.config.timeout := 1000              // 超时值
    i2cSlave.io.config.tsuData := 5                 // 数据建立时间
    i2cSlave.io.config.timeoutClear := False        // 不清除超时
    
    // 配置总线响应逻辑（简单的固定响应）
    i2cSlave.io.bus.rsp.valid := True   // 总是有效
    i2cSlave.io.bus.rsp.enable := True  // 总是使能
    i2cSlave.io.bus.rsp.data := False   // 数据输出为0
    
    // 连接中断信号
    io.interrupt := i2cSlave.io.timeout
  }
  
  // 运行仿真测试
  def runSimulation(): Unit = {
    try {
      println("正在编译I2C测试电路...")
      
      // 编译电路
      val compiled = SimConfig
        .withWave                    // 生成波形文件
        .workspacePath("simWorkspace/i2c_simple") // 设置工作目录
        .compile(SimpleI2cComponent())
      
      println("编译成功！开始仿真...")
      
      // 运行仿真
      compiled.doSim("i2c_basic_test") { dut =>
        println("初始化仿真环境...")
        
        // 启动时钟（10ns周期 = 100MHz）
        dut.clockDomain.forkStimulus(period = 10)
        
        // 等待复位完成
        dut.clockDomain.waitRisingEdge(5)
        
        println("\n=== 测试1: 检查初始状态 ===")
        println(s"SCL初始状态: ${dut.io.i2c.scl.read.toBoolean}")
        println(s"SDA初始状态: ${dut.io.i2c.sda.read.toBoolean}")
        println(s"中断状态: ${dut.io.interrupt.toBoolean}")
        
        // 验证初始状态
        assert(dut.io.i2c.scl.read.toBoolean == true, "SCL应该初始为高电平")
        assert(dut.io.i2c.sda.read.toBoolean == true, "SDA应该初始为高电平")
        assert(dut.io.interrupt.toBoolean == false, "初始状态不应该有中断")
        println("✓ 初始状态检查通过")
        
        println("\n=== 测试2: 时钟周期测试 ===")
        for (i <- 0 until 20) {
          dut.clockDomain.waitRisingEdge()
          if (i % 5 == 0) {
            println(s"周期 $i: SCL=${dut.io.i2c.scl.read.toBoolean}, " +
                   s"SDA=${dut.io.i2c.sda.read.toBoolean}, " +
                   s"中断=${dut.io.interrupt.toBoolean}")
          }
        }
        println("✓ 时钟周期测试完成")
        
        println("\n=== 测试3: I2C内部状态检查 ===")
        println(s"inFrame: ${dut.i2cSlave.io.internals.inFrame.toBoolean}")
        println(s"sdaRead: ${dut.i2cSlave.io.internals.sdaRead.toBoolean}")
        println(s"sclRead: ${dut.i2cSlave.io.internals.sclRead.toBoolean}")
        println("✓ 内部状态检查完成")
        
        println("\n=== 测试4: 总线命令接口测试 ===")
        println(s"总线命令类型: ${dut.i2cSlave.io.bus.cmd.kind}")
        println(s"总线命令数据: ${dut.i2cSlave.io.bus.cmd.data.toBoolean}")
        println(s"总线响应有效: ${dut.i2cSlave.io.bus.rsp.valid.toBoolean}")
        println(s"总线响应使能: ${dut.i2cSlave.io.bus.rsp.enable.toBoolean}")
        println(s"总线响应数据: ${dut.i2cSlave.io.bus.rsp.data.toBoolean}")
        println("✓ 总线接口测试完成")
        
        // 运行更多周期以观察行为
        println("\n=== 测试5: 长时间运行测试 ===")
        dut.clockDomain.waitRisingEdge(100)
        
        // 最终状态检查
        println(s"最终SCL状态: ${dut.io.i2c.scl.read.toBoolean}")
        println(s"最终SDA状态: ${dut.io.i2c.sda.read.toBoolean}")
        println(s"最终中断状态: ${dut.io.interrupt.toBoolean}")
        println("✓ 长时间运行测试完成")
        
        println("\n🎉 所有测试都成功通过！")
      }
      
      println("\n=== 测试总结 ===")
      println("✓ I2C从设备实例化成功")
      println("✓ 基本信号状态正确")
      println("✓ 内部逻辑工作正常")
      println("✓ 总线接口响应正确")
      println("✓ 仿真运行稳定")
      println("\n🎉 I2C最简测试案例全部完成！")
      
    } catch {
      case e: Exception =>
        println(s"\n❌ 测试过程中出现错误: ${e.getMessage}")
        println("错误详情:")
        e.printStackTrace()
        println("\n请检查:")
        println("1. SpinalHDL环境是否正确配置")
        println("2. I2C模块依赖是否完整")
        println("3. 仿真工具是否可用")
    }
  }
  
  // 主函数入口
  def main(): Unit = {
    println("开始运行I2C最简测试案例...")
    runSimulation()
  }
  
  // 直接运行
  main()
}

/**
 * 使用说明：
 * 
 * 1. 编译运行：
 *    在SpinalHDL项目根目录下运行：
 *    scala -cp "lib/target/scala-2.12/classes:core/target/scala-2.12/classes" spinal.tester.pending.I2cSimpleTest
 * 
 * 2. 查看波形：
 *    仿真完成后，可以在 simWorkspace/i2c_simple 目录下找到生成的波形文件
 * 
 * 3. 测试内容：
 *    - I2C从设备的基本实例化
 *    - 信号初始状态验证
 *    - 时钟周期运行测试
 *    - 内部状态监控
 *    - 总线接口功能验证
 * 
 * 4. 扩展建议：
 *    - 添加I2C主设备仿真
 *    - 实现START/STOP条件测试
 *    - 添加数据传输测试
 *    - 集成中断处理测试
 */
