package spinal.lib.bus.regif 

sealed trait AccessType

object AccessType {
  case object RO    extends AccessType  //- W: no effect, R: no effect
  case object RW    extends AccessType  //- W: as-is, R: no effect
  case object RC    extends AccessType  //- W: no effect, R: clears all bits
  case object RS    extends AccessType  //- W: no effect, R: sets all bits
  case object WR<PERSON>   extends AccessType  //- W: as-is, R: clears all bits
  case object WRS   extends AccessType  //- W: as-is, R: sets all bits
  case object WC    extends AccessType  //- W: clears all bits, R: no effect
  case object WS    extends AccessType  //- W: sets all bits, R: no effect
  case object WSRC  extends AccessType  //- W: sets all bits, R: clears all bits
  case object WCRS  extends AccessType  //- W: clears all bits, R: sets all bits
  case object W1C   extends AccessType  //- W: 1/0 clears/no effect on matching bit, R: no effect
  case object W1S   extends AccessType  //- W: 1/0 sets/no effect on matching bit, R: no effect
  case object W1T   extends AccessType  //- W: 1/0 toggles/no effect on matching bit, R: no effect
  case object W0C   extends AccessType  //- W: 1/0 no effect on/clears matching bit, R: no effect
  case object W0S   extends AccessType  //- W: 1/0 no effect on/sets matching bit, R: no effect
  case object W0T   extends AccessType  //- W: 1/0 no effect on/toggles matching bit, R: no effect
  case object W1SRC extends AccessType  //- W: 1/0 sets/no effect on matching bit, R: clears all bits
  case object W1CRS extends AccessType  //- W: 1/0 clears/no effect on matching bit, R: sets all bits
  case object W0SRC extends AccessType  //- W: 1/0 no effect on/sets matching bit, R: clears all bits
  case object W0CRS extends AccessType  //- W: 1/0 no effect on/clears matching bit, R: sets all bits
  case object WO    extends AccessType  //- W: as-is, R: error
  case object WOC   extends AccessType  //- W: clears all bits, R: error
  case object WOS   extends AccessType  //- W: sets all bits, R: error
  case object W1    extends AccessType  //- W: first one after ~HARD~ reset is as-is, other W have no effects, R: no effect
  case object WO1   extends AccessType  //- W: first one after ~HARD~ reset is as-is, other W have no effects, R: error
  case object NA    extends AccessType  // -W: reserved, R: reserved
  case object W1P   extends AccessType  // -W: 1/0 pulse/no effect on matching bit, R: no effect, pulse regNextOut after writehit
  case object W1I   extends AccessType  // -W: 1/0 pulse/no effect on matching bit, R: no effect, impulse combination out at writehit
  case object W0P   extends AccessType  // -W: 0/1 pulse/no effect on matching bit, R: no effect
  case object HSRW  extends AccessType  // HardWare Set then SoftWare RW, HW high priority than SW
  case object RWHS  extends AccessType  // SoftWare RW then HardWare Set, SW high priority than HW
  case object W1CHS extends AccessType  // SoftWare Write 1 Clear then HardWare Set, SW high priority than HW
  case object W1SHS extends AccessType  // SoftWare Write 1 Set  then HardWare Set, SW high priority than HW
  case object ROV   extends AccessType  // ReadOnly Value, used for constant
  case class  CSTM(name: String) extends AccessType {
    override def toString: String = if(name.isEmpty) "CSTM" else name.toUpperCase
  }
}

